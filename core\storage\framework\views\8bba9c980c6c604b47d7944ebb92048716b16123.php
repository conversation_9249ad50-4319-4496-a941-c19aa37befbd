<?php
    $testimonialContent = getContent('testimonial.content', true);
    $testimonialElement = getContent('testimonial.element', orderById: true);
?>

<section class="testimonials py-120">
    <div class="container">
        <div class="section-heading">
            <p class="section-heading__name"><?php echo e(__(@$testimonialContent->data_values->heading)); ?></p>
            <h2 class="section-heading__title text-dark"><?php echo e(__(@$testimonialContent->data_values->subheading)); ?></h2>
        </div>

        <div class="testimonial-slider">
            <?php $__currentLoopData = @$testimonialElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="testimonial-item">
                    <q class="testimonial-item__desc"><?php echo e(__(@$testimonial->data_values->quotes)); ?></q>
                    <div class="testimonial-item__info">
                        <div class="testimonial-item__thumb">
                            <img src="<?php echo e(getImage('assets/images/frontend/testimonial/' . @$testimonial->data_values->image, '60x60')); ?>" class="fit-image" alt="<?php echo app('translator')->get('image'); ?>" />
                        </div>
                        <div class="testimonial-item__details">
                            <p class="testimonial-item__name"><?php echo e(__(@$testimonial->data_values->name)); ?></p>
                            <span class="testimonial-item__designation"><?php echo e(__(@$testimonial->data_values->designation)); ?>, <?php echo e(__(@$testimonial->data_values->company_name)); ?></span>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/testimonial.blade.php ENDPATH**/ ?>