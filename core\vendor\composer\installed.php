<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'authorizenet/authorizenet' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'e1acf55c9cb22bef1852b1e494502973ade11cce',
            'type' => 'library',
            'install_path' => __DIR__ . '/../authorizenet/authorizenet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'btcpayserver/btcpayserver-greenfield-php' => array(
            'pretty_version' => 'v2.6.0',
            'version' => '2.6.0.0',
            'reference' => 'c115b0415719b9fe6e35d5df5f291646d4af2240',
            'type' => 'library',
            'install_path' => __DIR__ . '/../btcpayserver/btcpayserver-greenfield-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'carbonphp/carbon-doctrine-types' => array(
            'pretty_version' => '3.2.0',
            'version' => '3.2.0.0',
            'reference' => '18ba5ddfec8976260ead6e866180bd5d2f71aa1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../carbonphp/carbon-doctrine-types',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'coingate/coingate-php' => array(
            'pretty_version' => '3.0.5',
            'version' => '3.0.5.0',
            'reference' => 'c9e7f2c291cf8d5118c73028280aaa25a53c2302',
            'type' => 'library',
            'install_path' => __DIR__ . '/../coingate/coingate-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '0c5ccfcfea312b5c5a190a21ac5cef93f74baf99',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/package-versions-deprecated' => array(
            'pretty_version' => '1.11.99.5',
            'version' => '1.11.99.5',
            'reference' => 'b4f54f74ef3453349c24a845d22392cd31e65f1d',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./package-versions-deprecated',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f41715465d65213d644d3141a6a93081be5d3549',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.10',
            'version' => '2.0.10.0',
            'reference' => '5817d0659c5b50c9b950feb9af7b9668e2c436bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => '31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'adfb1f505deb6384dc8b39804c5065dd3c8c8c0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'ebaaf5be6c0286928352e054f2d5125608e5405e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ezyang/htmlpurifier' => array(
            'pretty_version' => 'v4.17.0',
            'version' => '4.17.0.0',
            'reference' => 'bbc513d79acf6691fa9cf10f192c90dd2957f18c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ezyang/htmlpurifier',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.1',
            'version' => '6.10.1.0',
            'reference' => '500501c2ce893c824c801da135d02661199f60c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fruitcake/php-cors' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => '3d158f36e7875e2f040f37bc0573956240a5a38b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fruitcake/php-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.2',
            'version' => '1.1.2.0',
            'reference' => 'fbd48bce38f73f8a4ec8583362e732e4095e5862',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.1',
            'version' => '7.8.1.0',
            'reference' => '41042bc7ab002487b876a0683fc8dce04ddce104',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'bbff78d96034045e58e13dedd6ad91b5d1253223',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.2',
            'version' => '2.6.2.0',
            'reference' => '45b30f99ac27b5ca93cb4831afe16285f57b8221',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/uri-template' => array(
            'pretty_version' => 'v1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'ecea8feef63bd4fef1f037ecb288386999ecc11c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/uri-template',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/conditionable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v9.52.16',
            ),
        ),
        'intervention/image' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '04be355f8d6734c826045d02a1079ad658322dad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laminas/laminas-diactoros' => array(
            'pretty_version' => '2.26.0',
            'version' => '2.26.0.0',
            'reference' => '6584d44eb8e477e89d453313b858daac6183cddc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laminas/laminas-diactoros',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laramin/utility' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '11c4e2b2731c3bb7c0e3f8db2689601a639d248e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laramin/utility',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v9.52.16',
            'version' => '9.52.16.0',
            'reference' => '082345d76fc6a55b649572efe10b11b03e279d24',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v2.15.1',
            'version' => '2.15.1.0',
            'reference' => '31fbe6f85aee080c4dc2f9b03dc6dd5d0ee72473',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.3',
            'version' => '1.3.3.0',
            'reference' => '3dbf8a8e914634c48d389c1234552666b3d43754',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/socialite' => array(
            'pretty_version' => 'v5.14.0',
            'version' => '5.14.0.0',
            'reference' => 'c7b0193a3753a29aff8ce80aa2f511917e6ed68a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.9.0',
            'version' => '2.9.0.0',
            'reference' => '502e0fe3f0415d06d5db1f83a472f0f3b754bafe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v3.4.6',
            'version' => '3.4.6.0',
            'reference' => '65ec5c03f7fee2c8ecae785795b829a15be48c2c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/clock' => array(
            'pretty_version' => '2.3.0',
            'version' => '2.3.0.0',
            'reference' => 'c7aadcd6fd97ed9e199114269c0be3f335e38876',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'lcobucci/jwt' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '55564265fddf810504110bd68ca311932324b0e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../lcobucci/jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.4.2',
            'version' => '2.4.2.0',
            'reference' => '91c24291965bd6d7c46c46a12ba7492f83b1cadf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '3.28.0',
            'version' => '3.28.0.0',
            'reference' => 'e611adab2b1ae2e3072fa72d62c62f52c2bf1f0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem-local' => array(
            'pretty_version' => '3.28.0',
            'version' => '3.28.0.0',
            'reference' => '13f22ea8be526ea58c2ddff9e158ef7c296e4f40',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem-local',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.15.0',
            'version' => '1.15.0.0',
            'reference' => 'ce0f4d1e8a6f4eb0ddff33f57c69c50fd09f4301',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth1-client' => array(
            'pretty_version' => 'v1.10.1',
            'version' => '1.10.1.0',
            'reference' => 'd6365b901b5c287dd41f143033315e2f777e1167',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth1-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mailjet/mailjet-apiv3-php' => array(
            'pretty_version' => 'v1.6.3',
            'version' => '1.6.3.0',
            'reference' => 'b10ac28c0c201bfd834e28a5a2c13f89d283aaa1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mailjet/mailjet-apiv3-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'messagebird/php-rest-api' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'reference' => 'f7c7ae490b0b2d9d228bac61b5d855c4623f7fad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../messagebird/php-rest-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mollie/laravel-mollie' => array(
            'pretty_version' => 'v2.25.0',
            'version' => '2.25.0.0',
            'reference' => 'd71e4764d7daaa8b6f1b3152f97ab73679278e0f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mollie/laravel-mollie',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mollie/mollie-api-php' => array(
            'pretty_version' => 'v2.67.1',
            'version' => '2.67.1.0',
            'reference' => 'ed5b2ba1dc8f30a4674f10ca78ad547c2df91008',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mollie/mollie-api-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.9.3',
            'version' => '2.9.3.0',
            'reference' => 'a30bfe2e142720dfa990d0a7e573997f5d884215',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.72.5',
            'version' => '2.72.5.0',
            'reference' => 'afd46589c216118ecd48ff2b95d77596af1e57ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'a6d3a6d1f545f01ef38e60f375d1cf1f4de98188',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd3ad0aa3b9f934602cb3e3902ebccf10be34d218',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.0.2',
            'version' => '5.0.2.0',
            'reference' => '139676794dc1e9231bf7bcd123cfc0c99182cb13',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/termwind' => array(
            'pretty_version' => 'v1.15.1',
            'version' => '1.15.1.0',
            'reference' => '8ab0b32c8caa4a2e09700ea32925441385e4a5dc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/termwind',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ocramius/package-versions' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '1.11.99',
            ),
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpmailer/phpmailer' => array(
            'pretty_version' => 'v6.9.1',
            'version' => '6.9.1.0',
            'reference' => '039de174cd9c17a8389754d3b877a2ed22743e18',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpmailer/phpmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.2',
            'version' => '1.9.2.0',
            'reference' => '80735db690fe4fc5c76dfa7f9b770634285fa820',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.37',
            'version' => '3.0.37.0',
            'reference' => 'cfa2013d0f68c062055180dd4328cc8b9d1f30b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
                1 => '1.0|2.0|3.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.3',
            'version' => '0.12.3.0',
            'reference' => 'b6b6cce7d3ee8fbf31843edce5e8f5a72eff4a73',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a4b48764bfbb8f3a6a4d1aeb1a35bb5e9ecac4a5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.6',
            'version' => '4.7.6.0',
            'reference' => '91039bc1faa45ba123c4328958e620d382ec7088',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'razorpay/razorpay' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'a3d7c2bcb416091edd6a76eb5a7600eaf00ac837',
            'type' => 'library',
            'install_path' => __DIR__ . '/../razorpay/razorpay',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.6',
            ),
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v2.0.11',
            'version' => '2.0.11.0',
            'reference' => '31435a468e2357e68df743f2527bda32556a0818',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/php-http-client' => array(
            'pretty_version' => '3.14.4',
            'version' => '3.14.4.0',
            'reference' => '6d589564522be290c7d7c18e51bcd8b03aeaf0b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/php-http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid' => array(
            'pretty_version' => '7.11.5',
            'version' => '7.11.5.0',
            'reference' => '1d2fd3b72687fe82264853a8888b014f8f99e81f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sendgrid/sendgrid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sendgrid/sendgrid-php' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'starkbank/ecdsa' => array(
            'pretty_version' => '0.0.5',
            'version' => '0.0.5.0',
            'reference' => '484bedac47bac4012dc73df91da221f0a66845cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../starkbank/ecdsa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stella-maris/clock' => array(
            'pretty_version' => '0.1.7',
            'version' => '*******',
            'reference' => 'fa23ce16019289a18bb3446fdecd45befcdd94f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stella-maris/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v7.128.0',
            'version' => '*********',
            'reference' => 'c704949c49b72985c76cc61063aa26fefbd2724e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'be5854cee0e8c7b110f00d695d11debdfa1a2a91',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '4b61b02fe15db48e3687ce1c45ea385d1780fe08',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '*******',
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'ef836152bf13472dc5fb5b08b0c0c4cfeddc0fcc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '8d7507f02b06e06815e56bb39aa0128e3806208b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '*******',
            'reference' => '8f93aec25d41b72493c6ddff14e916177c9efc50',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '3ef977a43883215d560a2cecb82ec8e62131471c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '27de8cc95e11db7a50b027e71caaab9024545947',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '6c519aa3f32adcfd1d1f18d923f6b227d9acf3c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '76326421d44c07f7824b19487cfbf87870b37efc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '618597ab8b78ac86d1c75a9d0b35540cda074f33',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '32a9da87d7b3245e09ac426c83d334ae9f06f80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'a287ed7475f85bf6f61890146edbc932c0fff919',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '861391a8da9a04cbad2d232ddd9e4893220d6e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '86fcae159633351e5fd145d1c47de6c528f8caff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-uuid' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '3abdd21b0ceaa3000ee950097bc3cf9efc137853',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '8d92dd79149f29e89ee0f480254db595f6a6a2c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '8a40d0f9b01f0fbb80885d3ce0ad6714fb603a58',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '*******',
            'reference' => 'bd1d9e59a81d8fa4acdcea3f617c581f7475a80f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'a147c0f826c4a1f3afb763ab8e009e37c877a44d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'a002933b13989fc4bd0b58e04bf7eec5210e438a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '*******',
            'reference' => 'b9d2189887bb6b2e0367a9fc7136c5239ab9b05a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/uid' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => '35904eca37a84bb764c560cbfcac9f0ac2bcdbdf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/uid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'dab2781371d54c86f6b25623ab16abb2dde2870c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.4.8',
            'version' => '*******',
            'reference' => 'ad23ca4312395f0a8a8633c831ef4c4ee542ed25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'textmagic/sdk' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'b81cd04df7ab6fd21f0ddd75bf987cf3f65fe9c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../textmagic/sdk',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => 'v2.2.7',
            'version' => '2.2.7.0',
            'reference' => '83ee6f38df0a63106a9e4536e3060458b74ccedb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twilio/sdk' => array(
            'pretty_version' => '6.44.4',
            'version' => '6.44.4.0',
            'reference' => '08aad5f377e2245b9cd7508e7762d95e7392fa4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twilio/sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.0',
            'version' => '5.6.0.0',
            'reference' => '2cf9fb6054c2bb1d59d1f3817706ecdb9d2934c4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vonage/client' => array(
            'pretty_version' => '2.4.0',
            'version' => '2.4.0.0',
            'reference' => '29f23e317d658ec1c3e55cf778992353492741d7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vonage/client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vonage/client-core' => array(
            'pretty_version' => '2.10.1',
            'version' => '2.10.1.0',
            'reference' => '0e5c6bf4af22cae60a3f1098b75c25d70bac242f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vonage/client-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vonage/nexmo-bridge' => array(
            'pretty_version' => '0.1.2',
            'version' => '0.1.2.0',
            'reference' => 'e9f63cd468b7e0edd73d0c90d0406d6b961f9eb7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vonage/nexmo-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
