<?php
    $faqContent = getContent('faq.content', true);
    $faqElement = collect(getContent('faq.element', orderById: true))->chunk(3);
?>

<section class="faq py-120">
    <div class="container">
        <div class="section-heading">
            <p class="section-heading__name"><?php echo e(__(@$faqContent->data_values->heading)); ?></p>
            <h3 class="section-heading__title"><?php echo e(__(@$faqContent->data_values->subheading)); ?></h3>
        </div>
        <div class="faq-wrapper">
            <div class="accordion faq--accordion" id="accordionExample">
                <div class="row g-3">
                    <div class="col-lg-6">
                        <?php $__currentLoopData = @$faqElement[0]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button <?php if(!$loop->first): ?> collapsed <?php endif; ?>" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($faq->id); ?>" <?php if($loop->first): ?> aria-expanded="true" <?php else: ?> aria-expanded="false" <?php endif; ?> aria-controls="collapse<?php echo e($faq->id); ?>">
                                        <?php echo e(__(@$faq->data_values->question)); ?>

                                        <span class="accordion-icon"><i class="las la-angle-down"></i></span>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo e($faq->id); ?>" class="accordion-collapse collapse <?php if($loop->first): ?> show <?php endif; ?>" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        <p><?php echo e(__(@$faq->data_values->answer)); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    <div class="col-lg-6">
                        <?php $__currentLoopData = @$faqElement[1]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($faq->id); ?>" aria-expanded="false" aria-controls="collapse<?php echo e($faq->id); ?>">
                                        <?php echo e(__(@$faq->data_values->question)); ?>

                                        <span class="accordion-icon"><i class="las la-angle-down"></i></span>
                                    </button>
                                </h2>
                                <div id="collapse<?php echo e($faq->id); ?>" class="accordion-collapse collapse" data-bs-parent="#accordionExample">
                                    <div class="accordion-body">
                                        <p><?php echo e(__(@$faq->data_values->answer)); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/faq.blade.php ENDPATH**/ ?>