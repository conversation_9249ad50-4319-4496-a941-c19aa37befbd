<?php
    $howWorkContent = getContent('how_to_work.content', true);
    $howWorkElement = getContent('how_to_work.element', null, 4, true);
?>
<section class="work-step py-120">
    <div class="container">
        <div class="section-heading">
            <p class="section-heading__name"><?php echo e(__(@$howWorkContent->data_values->heading)); ?></p>
            <h3 class="section-heading__title"><?php echo e(__(@$howWorkContent->data_values->subheading)); ?></h3>
        </div>

        <div class="work-step-wrapper">
            <?php $__currentLoopData = @$howWorkElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $howWork): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="work-step-card">
                    <p class="work-step-card__top">
                        <span class="icon">
                            <?php
                                echo @$howWork->data_values->icon;
                            ?>
                        </span>
                        <span class="count"><?php echo e($loop->iteration); ?></span>
                    </p>
                    <h5 class="work-step-card__title"><?php echo e(__(@$howWork->data_values->title)); ?></h5>
                    <p class="work-step-card__desc"><?php echo e(__(@$howWork->data_values->short_description)); ?></p>
                </div>
                <?php if(!$loop->last): ?>
                    <div class="work-step-shape">
                        <img src="<?php echo e(getImage($activeTemplateTrue . 'images/thumbs/step-arrow.png')); ?>" alt="<?php echo app('translator')->get('image'); ?>" />
                    </div>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/how_to_work.blade.php ENDPATH**/ ?>