<?php
    $socialLinks = getContent('social_icon.element', orderById: true);
    $footerContent = getContent('footer.content', true);
    $policyPages = getContent('policy_pages.element', orderById: true);
    $contactContent = getContent('contact_us.content', true);
?>
<footer class="footer-area">
    <div class="footer-wrapper bg-img py-120" data-background-image="<?php echo e(getImage('assets/images/frontend/footer/' . @$footerContent->data_values->image, '1905x535')); ?>">
        <div class="container">
            <div class="row justify-content-between gy-5">
                <div class="col-xl-3 col-sm-6">
                    <div class="footer-item">
                        <div class="footer-item__logo">
                            <a href="<?php echo e(route('home')); ?>"><img src="<?php echo e(siteLogo('white')); ?>" alt="<?php echo app('translator')->get('image'); ?>" /></a>
                        </div>
                        <p class="footer-item__desc"><?php echo e(__(@$footerContent->data_values->description)); ?></p>
                        <ul class="footer-contact-menu">
                            <li class="footer-contact-menu__item">
                                <div class="footer-contact-menu__item-icon">
                                    <i class="icon-Vector-4"></i>
                                </div>
                                <div class="footer-contact-menu__item-content">
                                    <a href="tel:<?php echo e(@$contactContent->data_values->contact_number); ?>"><?php echo e(@$contactContent->data_values->contact_number); ?></a>
                                </div>
                            </li>
                            <li class="footer-contact-menu__item">
                                <div class="footer-contact-menu__item-icon">
                                    <i class="icon-Vector-3"></i>
                                </div>
                                <div class="footer-contact-menu__item-content">
                                    <a href="mailto:<?php echo e(@$contactContent->data_values->contact_email); ?>"><?php echo e(@$contactContent->data_values->contact_email); ?></a>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-xl-2 col-sm-6 col-xsm-6">
                    <div class="footer-item">
                        <h5 class="footer-item__title"><?php echo app('translator')->get('Quick Links'); ?></h5>
                        <ul class="footer-menu">
                            <?php $__currentLoopData = @$policyPages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $policy): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="footer-menu__item">
                                    <a href="<?php echo e(route('policy.pages', [slug(@$policy->data_values->title), @$policy->id])); ?>" class="footer-menu__link"><?php echo e(__(@$policy->data_values->title)); ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <div class="col-xl-2 col-sm-6 col-xsm-6">
                    <div class="footer-item">
                        <h5 class="footer-item__title"><?php echo app('translator')->get('Vehicles'); ?></h5>
                        <ul class="footer-menu">
                            <?php $__currentLoopData = @$vehicleTypes->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicleType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li class="footer-menu__item">
                                    <a href="<?php echo e(route('vehicles', $vehicleType->slug)); ?>" class="footer-menu__link"><?php echo e(__($vehicleType->name)); ?></a>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <div class="col-xl-3 col-sm-6">
                    <div class="footer-item">
                        <div class="footer-item-social">
                            <p class="footer-item-social__title"><?php echo app('translator')->get('Contact With Us'); ?></p>
                            <ul class="social-list mt-0">
                                <?php $__currentLoopData = @$socialLinks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $socialLink): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="social-list__item">
                                        <a href="<?php echo e(@$socialLink->data_values->url); ?>" class="social-list__link flex-center" target="_blank">
                                            <?php
                                                echo @$socialLink->data_values->social_icon;
                                            ?>
                                        </a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                    <div class="footer-newsletter">
                        <h5 class="footer-newsletter__title"><?php echo e(__(@$footerContent->data_values->subscribe_title)); ?></h5>
                        <p class="footer-newsletter__desc"><?php echo e(__(@$footerContent->data_values->subscribe_subtitle)); ?></p>
                    </div>
                    <form class="footer-newsletter-form">
                        <input type="email" name="email" class="form--control" placeholder="<?php echo app('translator')->get('Email Address'); ?>" autocomplete="off" />
                        <button type="button" class="btn btn--gradient subscribeBtn"><?php echo app('translator')->get('Subscribe'); ?></button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-footer py-3">
        <div class="container">
            <div class="row gy-3">
                <div class="col-md-12 text-center">
                    <div class="bottom-footer-text text-white">
                        <?php echo app('translator')->get('Copyright'); ?> &copy; <?php echo date('Y') ?> <?php echo app('translator')->get('All rights reserved.'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $('.subscribeBtn').on('click', function(e) {
                e.preventDefault()
                var email = $('[name=email]').val();
                if (!email) {
                    return;
                }
                $.ajax({
                    url: "<?php echo e(route('subscribe')); ?>",
                    method: "POST",
                    data: {
                        _token: "<?php echo e(csrf_token()); ?>",
                        email: email
                    },
                    success: function(response) {
                        if (response.success) {
                            $('[name=email]').val('')
                            notify('success', response.success);
                        } else {
                            notify('error', response.error);
                        }
                    }
                });
            });
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/partials/footer.blade.php ENDPATH**/ ?>