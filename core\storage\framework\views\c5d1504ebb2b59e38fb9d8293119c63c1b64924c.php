<?php if(gs('multi_language')): ?>
    <?php
        $languages = App\Models\Language::all();
        $language = $languages->where('code', '!=', session('lang'));
        $activeLanguage = $languages->where('code', session('lang'))->first();
    ?>
    <div class="language__icon">
        <i class="fas fa-globe"></i>
    </div>
    <div class="language__wrapper" data-bs-toggle="dropdown" aria-expanded="false">
        <p class="language__text"><?php echo e(__(@$activeLanguage->name)); ?></p>
        <span class="language__arrow"><i class="fas fa-chevron-down"></i></span>
    </div>
    <div class="dropdown-menu">
        <ul class="language-list">
            <?php $__currentLoopData = $language; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="language-list__item langSel" data-lang_code="<?php echo e($item->code); ?>">
                    <p class="language_text"><?php echo e(__(@$item->name)); ?></p>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/partials/language.blade.php ENDPATH**/ ?>