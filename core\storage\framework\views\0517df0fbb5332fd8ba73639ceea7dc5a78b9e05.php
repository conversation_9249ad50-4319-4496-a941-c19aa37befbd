<?php $__currentLoopData = @$blogs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $blog): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="col-md-6">
        <a href="<?php echo e(route('blog.details', [slug(@$blog->data_values->title), $blog->id])); ?>" class="blog-item">
            <div class="blog-item__wrapper">
                <div class="blog-item__thumb">
                    <img src="<?php echo e(getImage('assets/images/frontend/blog/thumb_' . @$blog->data_values->image, '485x245')); ?>" class="fit-image" alt="<?php echo app('translator')->get('image'); ?>" />
                </div>
                <div class="blog-item__content">
                    <h5 class="blog-item__title border-effect"><?php echo e(__(@$blog->data_values->title)); ?></h5>
                    <p class="blog-item__desc">
                        <?php
                            echo strLimit(strip_tags(@$blog->data_values->description), 60);
                        ?>
                    </p>
                    <div class="blog-auth">
                        <div class="blog-auth__content">
                            <ul class="blog-publish">
                                <li class="blog-publish__date"><?php echo e(showDateTime(@$blog->created_at, 'd M Y')); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/partials/blog.blade.php ENDPATH**/ ?>