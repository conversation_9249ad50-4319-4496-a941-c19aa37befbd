<?php
    $credentials = $general->socialite_credentials;
    $type = Request::is('user.registe') ? 'Register' : 'Login';
?>
<?php if(@$credentials->google->status == Status::ENABLE || @$credentials->facebook->status == Status::ENABLE || @$credentials->linkedin->status == Status::ENABLE): ?>
    <div class="auth-devide">
        <span>OR</span>
    </div>
    <div class="auth--btn-wrapper">
        <?php if(@$credentials->google->status == Status::ENABLE): ?>
            <div class="auth--btn-inner">
                <a class="btn auth--btn w-100" href="<?php echo e(route('user.social.login', 'google')); ?>">
                    <span class="icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/google.svg')); ?>" alt="">
                    </span>
                    <?php echo app('translator')->get('Google'); ?>
                </a>
            </div>
        <?php endif; ?>
        <?php if(@$credentials->facebook->status == Status::ENABLE): ?>
            <div class="auth--btn-inner">
                <a class="btn auth--btn w-100" href="<?php echo e(route('user.social.login', 'facebook')); ?>">
                    <span class="icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/facebook.svg')); ?>" alt="">
                    </span>
                    <?php echo app('translator')->get('Facebook'); ?>
                </a>
            </div>
        <?php endif; ?>
        <?php if(@$credentials->linkedin->status == Status::ENABLE): ?>
            <div class="auth--btn-inner">
                <a class="btn auth--btn w-100" href="<?php echo e(route('user.social.login', 'linkedin')); ?>">
                    <span class="icon">
                        <img src="<?php echo e(asset($activeTemplateTrue . 'images/linkdin.svg')); ?>" alt="">
                    </span>
                    <?php echo app('translator')->get('Linkedin'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/partials/social_login.blade.php ENDPATH**/ ?>