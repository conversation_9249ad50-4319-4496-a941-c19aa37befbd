<?php $__env->startSection('app'); ?>
    <?php
        $loginContent = getContent('login.content', true);
    ?>
    <section class="account">
        <div class="account-inner">
            <div class="account-thumb">
                <img src="<?php echo e(getImage('assets/images/frontend/login/' . @$loginContent->data_values->image, '740x600')); ?>" alt="<?php echo app('translator')->get('image'); ?>">
            </div>
            <div class="account-tab">
                <a class="account-logo m-0" href="<?php echo e(route('home')); ?>"><img src="<?php echo e(siteLogo()); ?>" alt="<?php echo app('translator')->get('image'); ?>" /></a>
                <div class="account-tab__inner">
                    <ul class="nav custom--tab">
                        <li class="tab__bar"></li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" type="button"><?php echo app('translator')->get('Login'); ?></button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" href="<?php echo e(route('user.register')); ?>"><?php echo app('translator')->get('Register'); ?></a>
                        </li>
                    </ul>
                    <div class="account-form">
                        <div class="account-form__content mb-4">
                            <h2 class="account-form__title"><?php echo e(__(@$loginContent->data_values->heading)); ?></h2>
                            <p class="account-form__desc fs-18"><?php echo e(__(@$loginContent->data_values->subheading)); ?></p>
                        </div>
                        <form method="POST" action="<?php echo e(route('user.login')); ?>" class="verify-gcaptcha">
                            <?php echo csrf_field(); ?>
                            <div class="form-group">
                                <label for="email" class="form--label"><?php echo app('translator')->get('Username or Email'); ?></label>
                                <input type="text" id="email" name="username" value="<?php echo e(old('username')); ?>" class="form--control" required>
                            </div>
                            <div class="form-group">
                                <label for="password" class="form--label"><?php echo app('translator')->get('Password'); ?></label>
                                <input id="password" type="password" class="form--control" name="password" required>
                            </div>

                            <?php if (isset($component)) { $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243 = $component; } ?>
<?php $component = App\View\Components\Captcha::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('captcha'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Captcha::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243)): ?>
<?php $component = $__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243; ?>
<?php unset($__componentOriginalc0af13564821b3ac3d38dfa77d6cac9157db8243); ?>
<?php endif; ?>

                            <div class="form-group d-flex justify-content-between align-items-center flex-wrap">
                                <div>
                                    <input class="form-check-input" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="remember"><?php echo app('translator')->get('Remember Me'); ?></label>
                                </div>
                                <a class="text--base" href="<?php echo e(route('user.password.request')); ?>"> <?php echo app('translator')->get('Forgot your password?'); ?></a>
                            </div>

                            <button type="submit" id="recaptcha" class="btn btn--gradient w-100"><?php echo app('translator')->get('Submit'); ?></button>
                        </form>
                        <?php echo $__env->make($activeTemplate . 'partials.social_login', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";
            $('[name=captcha]').removeClass('form-control').siblings('label').removeClass('form-label').addClass('form--label');
        })(jQuery)
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make($activeTemplate . 'layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/user/auth/login.blade.php ENDPATH**/ ?>