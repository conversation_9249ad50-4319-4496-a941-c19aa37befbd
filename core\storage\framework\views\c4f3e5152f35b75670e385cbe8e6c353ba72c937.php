<header class="header" id="header">
    <div class="container">
        <nav class="navbar navbar-expand-lg">
            <a class="navbar-brand logo m-0" href="<?php echo e(route('home')); ?>"><img src="<?php echo e(siteLogo()); ?>" alt="<?php echo app('translator')->get('image'); ?>" /></a>
            <button class="navbar-toggler header-button" type="button" data-bs-toggle="offcanvas"
                    data-bs-target="#offcanvasDarkNavbar" aria-controls="offcanvasDarkNavbar"
                    aria-label="Toggle navigation">
                <span id="hiddenNav"><i class="las la-bars"></i></span>
            </button>
            <div class="offcanvas border-0 offcanvas-start" tabindex="-1" id="offcanvasDarkNavbar">
                <div class="offcanvas-header">
                    <a class="logo navbar-brand" href="<?php echo e(route('home')); ?>"><img src="<?php echo e(siteLogo()); ?>" alt="<?php echo app('translator')->get('image'); ?>" /></a>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas"
                            aria-label="Close"></button>
                </div>
                <div class="offcanvas-body">
                    <ul class="navbar-nav w-100 justify-content-lg-center nav-menu align-items-lg-center">
                        <li class="navbar-nav__bar"></li>
                        <li class="language dropdown d-lg-none">
                            <?php echo $__env->make($activeTemplate . 'partials.language', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        </li>
                        <li class="nav-item <?php echo e(menuActive('home')); ?>">
                            <a class="nav-link " aria-current="page" href="<?php echo e(route('home')); ?>"><?php echo app('translator')->get('Home'); ?></a>
                        </li>
                        <?php
                            $pages = App\Models\Page::where('tempname', $activeTemplate)
                                ->where('is_default', Status::NO)
                                ->get();
                        ?>
                        <?php $__currentLoopData = $pages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="nav-item <?php if($data->slug == Request::segment(1)): ?> active <?php endif; ?>">
                                <a class="nav-link"
                                   href="<?php echo e(route('pages', [$data->slug])); ?>"><?php echo e(__($data->name)); ?></a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link" href="#" role="button" data-bs-toggle="dropdown"
                               aria-expanded="false">
                                <?php echo app('translator')->get('Vehicles'); ?>
                                <span class="nav-item__icon"><i class="las la-angle-down"></i></span>
                            </a>
                            <ul class="dropdown-menu header-dropdown">
                                <?php $__currentLoopData = $vehicleTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vehicleType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="dropdown-menu__list">
                                        <a class="dropdown-item dropdown-menu__link" href="<?php echo e(route('vehicles', $vehicleType->slug)); ?>"><?php echo e(__($vehicleType->name)); ?></a>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </li>
                        <li class="nav-item <?php echo e(menuActive('blog')); ?>">
                            <a class="nav-link" href="<?php echo e(route('blog')); ?>"><?php echo app('translator')->get('Blog'); ?></a>
                        </li>
                        <li class="nav-item <?php echo e(menuActive('contact')); ?>">
                            <a class="nav-link" href="<?php echo e(route('contact')); ?>"><?php echo app('translator')->get('Contact'); ?></a>
                        </li>
                        <li class="nav-item mt-4 d-lg-none">
                            <div class="header-right d-lg-none">
                                <div class="header-button flex-align gap-3">
                                    <?php if(auth()->guard()->check()): ?>
                                        <a href="<?php echo e(route('user.home')); ?>" class="btn btn-outline--base-two"><?php echo app('translator')->get('Dashboard'); ?></a>
                                        <a href="<?php echo e(route('user.logout')); ?>" class="btn btn--gradient"><?php echo app('translator')->get('Logout'); ?></a>
                                    <?php else: ?>
                                        <a href="<?php echo e(route('user.login')); ?>" class="btn btn-outline--base-two"><?php echo app('translator')->get('Login'); ?></a>
                                        <a href="<?php echo e(route('user.register')); ?>" class="btn btn--gradient"><?php echo app('translator')->get('Register'); ?></a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="header-right d-none d-lg-block">
                <div class="header-button flex-align gap-3">
                    <div class="language dropdown d-none d-lg-flex">
                        <?php echo $__env->make($activeTemplate . 'partials.language', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('user.home')); ?>" class="btn btn-outline--base-two"><?php echo app('translator')->get('Dashboard'); ?></a>
                        <a href="<?php echo e(route('user.logout')); ?>" class="btn btn--gradient"><?php echo app('translator')->get('Logout'); ?></a>
                    <?php else: ?>
                        <a href="<?php echo e(route('user.login')); ?>" class="btn btn-outline--base-two"><?php echo app('translator')->get('Login'); ?></a>
                        <a href="<?php echo e(route('user.register')); ?>" class="btn btn--gradient"><?php echo app('translator')->get('Register'); ?></a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </div>
</header>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/partials/header.blade.php ENDPATH**/ ?>