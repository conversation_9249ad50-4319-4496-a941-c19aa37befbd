<?php
    $aboutContent = getContent('about.content', true);
    $aboutElement = getContent('about.element', orderById: true);
?>
<section class="about-section py-120">
    <div class="container">
        <div class="row gy-4 align-items-center">
            <div class="col-lg-6">
                <div class="about-section__tumb">
                    <img class="fit-image" src="<?php echo e(getImage('assets/images/frontend/about/' . @$aboutContent->data_values->image, '635x530')); ?>" alt="<?php echo app('translator')->get('image'); ?>">
                </div>
            </div>
            <div class="col-lg-6">
                <div class="about-content">
                    <p class="about-content__subtitle"><?php echo e(__(@$aboutContent->data_values->heading)); ?></p>
                    <h2 class="about-content__title"><?php echo e(__(@$aboutContent->data_values->subheading)); ?></h2>
                    <p class="about-content__desc"><?php echo e(__(@$aboutContent->data_values->description)); ?></p>
                    <div class="about-counter">
                        <?php $__currentLoopData = @$aboutElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $about): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="about-counter-item">
                                <span class="about-counter-item__icon">
                                    <?php
                                        echo @$about->data_values->icon;
                                    ?>
                                </span>
                                <div class="about-counter-item__content">
                                    <h1 class="about-counter-item__count"><?php echo e(@$about->data_values->amount); ?></h1>
                                    <span class="about-counter-item__title"><?php echo __(implode('<br>', explode(' ', @$about->data_values->title))) ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/about.blade.php ENDPATH**/ ?>