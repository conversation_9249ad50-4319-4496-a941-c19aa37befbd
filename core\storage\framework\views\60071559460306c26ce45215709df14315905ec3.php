<?php
    $ctaContact = getContent('cta_contact.content', true);
    $ctaElement = getContent('cta_contact.element', orderById: true);
?>

<section class="cta-contact py-120">
    <div class="container">
        <div class="row gy-4 align-items-center justify-content-center">
            <div class="col-lg-6">
                <div class="cta-contact-info-left">
                    <h2 class="cta-contact__title"><?php echo e(__(@$ctaContact->data_values->heading)); ?></h2>
                    <p class="cta-contact-info__subtitle">
                        <?php echo e(__(@$ctaContact->data_values->subheading)); ?>

                    </p>
                    <ul class="cta-contact-info__list">
                        <?php $__currentLoopData = @$ctaElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cta): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><i class="las la-check-square"></i> <?php echo e(__(@$cta->data_values->title)); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                    <div class="cta-contact-info__btn">
                        <a class="btn btn--black" href="<?php echo e(url(@$ctaContact->data_values->contact_button_link)); ?>">
                            <?php echo e(__(@$ctaContact->data_values->contact_button)); ?> <span><i class="las la-chevron-circle-right"></i></span>
                        </a>
                    </div>

                </div>
            </div>

            <div class="col-lg-6">
                <div class="cta-contact-thumb">
                    <img src="<?php echo e(getImage('assets/images/frontend/cta_contact/' . @$ctaContact->data_values->image, '640x380')); ?>" alt="image">
                </div>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/cta_contact.blade.php ENDPATH**/ ?>