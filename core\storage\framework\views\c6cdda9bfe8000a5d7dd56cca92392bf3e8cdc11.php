<?php
    $serviceContent = getContent('service.content', true);
    $serviceElement = getContent('service.element', orderById: true);
?>
<section class="choose-us py-120 bg-img" data-background-image="<?php echo e(getImage('assets/images/frontend/service/' . @$serviceContent->data_values->background_image, '1905x995')); ?>">
    <div class="container">
        <div class="section-heading">
            <p class="section-heading__name name-base"><?php echo e(__(@$serviceContent->data_values->heading)); ?></p>
            <h3 class="section-heading__title text-white"><?php echo e(__(@$serviceContent->data_values->subheading)); ?></h3>
        </div>
        <div class="choose-us-tabs">
            <ul class="nav nav-tabs style-two" role="tablist">
                <?php $__currentLoopData = @$serviceElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php if($loop->first): ?> active <?php endif; ?>" data-bs-toggle="tab" data-bs-target="#tab<?php echo e(@$service->id); ?>" type="button" role="tab" aria-selected="true">
                            <span class="icon">
                                <?php
                                    echo @$service->data_values->icon;
                                ?>
                            </span>
                            <span class="title"><?php echo e(__(@$service->data_values->title)); ?></span>
                        </button>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <div class="tab-content">
                <?php $__currentLoopData = @$serviceElement; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="tab-pane <?php if($loop->first): ?> show active <?php endif; ?>" id="tab<?php echo e(@$service->id); ?>">
                        <div class="row g-4 g-xl-5">
                            <div class="col-lg-6">
                                <div class="choose-thumb">
                                    <img class="fit-image" src="<?php echo e(getImage('assets/images/frontend/service/' . @$service->data_values->image, '625x370')); ?>" alt="<?php echo app('translator')->get('image'); ?>" />
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="choose-content">
                                    <h3 class="choose-content__title"><?php echo e(__(ucwords(@$service->data_values->title))); ?></h3>
                                    <p>
                                        <?php
                                            echo @$service->data_values->description;
                                        ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\agapeconnect\core\resources\views/templates/basic/sections/service.blade.php ENDPATH**/ ?>